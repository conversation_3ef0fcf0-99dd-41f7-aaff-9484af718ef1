{"name": "ai-chatbot-monorepo", "private": true, "version": "1.0.0", "description": "AI Chatbot with Node.js backend and React TypeScript frontend", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "vite", "dev:backend": "cd backend && npm run dev", "install:backend": "cd backend && npm install", "build": "tsc && vite build", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"lucide-react": "^0.525.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "sass": "^1.89.2", "typescript": "^5.2.2", "vite": "^7.0.4"}}