import React, { useEffect, useState } from 'react';
import ChatbotContainer from '@/components/ChatbotContainer';
import BackgroundRefresh from '@/components/BackgroundRefresh';
import NotificationContainer from '@/components/NotificationContainer';
import { useUnsplashBackground } from '@/hooks/useUnsplashBackground.tsx';
import { useNotification } from '@/hooks/useNotification';
import baseStyles from '@/styles/base.module.scss';
import layoutStyles from '@/styles/layout.module.scss';

const App: React.FC = () => {
  const { refreshBackground, isLoading: backgroundLoading, attributionElement } = useUnsplashBackground();
  const { notifications, showNotification, hideNotification } = useNotification();

  // Splash animation state
  const [showSplash, setShowSplash] = useState(true);
  const [fadeSplash, setFadeSplash] = useState(false);

  useEffect(() => {
    // Start fade out after 3.5s, finish at 4s
    const fadeTimer = setTimeout(() => setFadeSplash(true), 3500);
    const hideTimer = setTimeout(() => setShowSplash(false), 4000);
    return () => {
      clearTimeout(fadeTimer);
      clearTimeout(hideTimer);
    };
  }, []);

  let splashClass = baseStyles.splashOverlayV2;
  if (fadeSplash && baseStyles['splashOverlayV2--fade']) splashClass += ' ' + baseStyles['splashOverlayV2--fade'];

  return (
    <div className={layoutStyles.app}>
      {showSplash && (
        <div className={splashClass}>
          <div className={baseStyles.splashLogoWrapper}>
            <img src="/Logo.jpg" alt="Logo" className={baseStyles.splashLogoV2} />
          </div>
        </div>
      )}

      {/* <BackgroundRefresh 
        onRefresh={refreshBackground} 
        isLoading={backgroundLoading}
      /> */}
      
      <ChatbotContainer 
        onNotification={showNotification}
      />
      
      <NotificationContainer
        notifications={notifications}
        onClose={hideNotification}
      />
      
      {attributionElement}
    </div>
  );
};

export default App;
