@use 'variables' as *;

/* Popup Modal Styles with Dark Glass Effects */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px) var(--backdrop-saturate-high);
  -webkit-backdrop-filter: blur(8px) var(--backdrop-saturate-high);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-dropdown);
  padding: var(--spacing-xl);
  animation: fadeInGlass var(--transition-normal) ease-out;

  @include responsive('tablet') {
    padding: 15px;
  }

  @include responsive('mobile') {
    padding: var(--spacing-xs);
  }
}

.modal {
  position: relative;
  background: var(--bg-modal);
  @include glass-border();
  border-radius: var(--radius-2xl);
  box-shadow: 
    var(--shadow-2xl),
    0 10px 20px rgba(0, 0, 0, 0.3),
    var(--shadow-glass);
  @include glass-effect(0, 25px);
  max-width: 700px;
  width: 100%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  animation: slideUpGlass 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  @include gradient-border(var(--gradient-rainbow), var(--radius-2xl));

  &::before {
    animation: modalGradientShift var(--animation-very-slow) ease infinite;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at 30% 20%,
      rgba(255, 255, 255, 0.08) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 80%,
      rgba(14, 165, 233, 0.1) 0%,
      transparent 50%
    );
    border-radius: var(--radius-2xl);
    pointer-events: none;
    z-index: 1;
  }

  @include responsive('tablet') {
    max-height: 90vh;
    border-radius: var(--radius-xl);

    &::before,
    &::after {
      border-radius: var(--radius-xl);
    }
  }

  @include responsive('mobile') {
    max-height: 95vh;
    border-radius: var(--radius-lg);

    &::before,
    &::after {
      border-radius: var(--radius-lg);
    }
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3xl);
  border-bottom: 1px solid var(--border-primary);
  margin-bottom: 0;
  position: relative;
  z-index: 2;

  @include responsive('tablet') {
    padding: var(--spacing-2xl) var(--spacing-2xl) 0 var(--spacing-2xl);
  }

  @include responsive('mobile') {
    padding: var(--spacing-xl) var(--spacing-xl) 0 var(--spacing-xl);
  }
}

.title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  background: var(--gradient-text);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);

  @include responsive('tablet') {
    font-size: var(--font-size-xl);
  }
}

.closeButton {
  @include glass-effect(0.1);
  @include glass-border();
  font-size: var(--font-size-2xl);
  color: var(--text-tertiary);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  @include glass-effect(0.1, 10px);
  position: relative;
  overflow: hidden;
  @include shimmer-effect();

  &:hover {
    @include glass-effect(0.15);
    border-color: var(--border-secondary);
    color: var(--text-primary);
    transform: scale(1.05);
  }
}

.content {
  padding: var(--spacing-3xl);
  overflow-y: auto;
  flex: 1;
  position: relative;
  z-index: 2;

  @include responsive('tablet') {
    padding: var(--spacing-2xl);
  }

  @include responsive('mobile') {
    padding: var(--spacing-xl);
  }
}

.imageAnalyzedBadge {
  background: rgba(3, 105, 161, 0.2);
  border: 1px solid rgba(3, 105, 161, 0.4);
  color: #60a5fa;
  padding: var(--spacing-xs) var(--spacing-base);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xl);
  display: inline-flex;
  align-items: center;
  gap: 6px;
  @include glass-effect(0, 10px);
  box-shadow: 0 4px 12px rgba(3, 105, 161, 0.2);
}

.responseContent {
  font-size: var(--font-size-md);
  line-height: var(--line-height-normal);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-3xl);
  white-space: pre-wrap;
}

.followUpSection {
  border-top: 1px solid var(--border-primary);
  padding-top: var(--spacing-3xl);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--color-secondary), transparent);
  }
}

.followUpHistory {
  margin-bottom: var(--spacing-2xl);
}

.followUpItem {
  @include glass-effect(0.05);
  @include glass-border();
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  @include glass-effect(0.05, 10px);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-secondary);
  }
}

.followUpQuestion {
  font-weight: var(--font-weight-semibold);
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-base);
}

.followUpResponse {
  color: var(--text-tertiary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-tight);
}

.followUpInput {
  display: flex;
  gap: var(--spacing-lg);
  align-items: flex-end;

  @include responsive('tablet') {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }
}

.followUpTextarea {
  flex: 1;
  min-height: 90px;
  padding: var(--spacing-lg);
  @include glass-effect(0.05);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-family: inherit;
  color: var(--text-secondary);
  resize: vertical;
  transition: var(--transition-normal);
  @include glass-effect(0.05, 10px);

  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }

  &:focus {
    outline: none;
    border-color: var(--color-secondary);
    box-shadow: 
      0 0 0 4px rgba(131, 56, 236, 0.2),
      0 4px 12px rgba(131, 56, 236, 0.3);
    @include glass-effect(0.08);
  }

  &:disabled {
    background: rgba(255, 255, 255, 0.02);
    color: var(--text-disabled);
    cursor: not-allowed;
  }
}

.sendButton {
  position: relative;
  background: var(--gradient-secondary);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: var(--transition-normal);
  white-space: nowrap;
  height: fit-content;
  box-shadow: 0 4px 12px rgba(131, 56, 236, 0.3);
  overflow: hidden;
  @include shimmer-effect();

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--color-secondary-light), var(--color-accent-blue-dark));
    @include hover-lift();
    box-shadow: 0 8px 20px rgba(131, 56, 236, 0.4);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    @include glass-effect(0.1);
    color: var(--text-disabled);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  @include responsive('tablet') {
    width: 100%;
    justify-content: center;
  }
}

.loadingSpinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: var(--radius-full);
  animation: spin 5s linear infinite;
  margin-right: var(--spacing-sm);
}

.errorMessage {
  @include glass-effect(0, 10px);
  background: rgba(220, 38, 38, 0.1);
  border: 1px solid rgba(220, 38, 38, 0.3);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  color: #fca5a5;
  margin-bottom: var(--spacing-xl);
}

/* End Prompt Section Styles */
.endPromptSection {
  margin-top: var(--spacing-3xl);
  padding-top: var(--spacing-3xl);
  border-top: 1px solid var(--border-primary);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--color-accent-green), transparent);
  }
}

.ctaCard {
  @include glass-effect(0.08);
  @include glass-border();
  border-radius: var(--radius-xl);
  padding: var(--spacing-3xl);
  text-align: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, 
    rgba(34, 197, 94, 0.05) 0%, 
    rgba(16, 185, 129, 0.05) 50%, 
    rgba(6, 182, 212, 0.05) 100%
  );
  border: 1px solid rgba(34, 197, 94, 0.2);
  box-shadow: 
    0 8px 32px rgba(34, 197, 94, 0.1),
    0 4px 16px rgba(16, 185, 129, 0.1);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at 50% 0%,
      rgba(34, 197, 94, 0.1) 0%,
      transparent 70%
    );
    pointer-events: none;
  }

  h3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    background: linear-gradient(135deg, #22c55e, #10b981, #06b6d4);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0 0 var(--spacing-lg) 0;
    position: relative;
    z-index: 1;

    @include responsive('tablet') {
      font-size: var(--font-size-xl);
    }
  }

  p {
    color: var(--text-secondary);
    font-size: var(--font-size-md);
    line-height: var(--line-height-normal);
    margin: 0 0 var(--spacing-2xl) 0;
    position: relative;
    z-index: 1;
  }
}

.ctaButton {
  position: relative;
  background: linear-gradient(135deg, #22c55e, #10b981);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl) var(--spacing-3xl);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: var(--transition-normal);
  box-shadow: 
    0 4px 16px rgba(34, 197, 94, 0.3),
    0 2px 8px rgba(16, 185, 129, 0.2);
  overflow: hidden;
  @include shimmer-effect();
  z-index: 1;

  &:hover {
    background: linear-gradient(135deg, #16a34a, #059669);
    transform: translateY(-2px);
    box-shadow: 
      0 8px 24px rgba(34, 197, 94, 0.4),
      0 4px 12px rgba(16, 185, 129, 0.3);
  }

  &:active {
    transform: translateY(0);
  }

  @include responsive('tablet') {
    width: 100%;
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: var(--font-size-base);
  }
}

.ctaSubtext {
  color: var(--text-tertiary);
  font-size: var(--font-size-sm);
  margin: var(--spacing-lg) 0 0 0;
  position: relative;
  z-index: 1;
}

/* Animations */
@keyframes fadeInGlass {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
}

@keyframes slideUpGlass {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.9);
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    backdrop-filter: blur(25px);
  }
}

@keyframes modalGradientShift {
  0%, 100% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 200% 50%; }
  75% { background-position: 300% 50%; }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .modal {
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(0, 0, 0, 0.95);
  }
  
  .closeButton {
    border-color: rgba(255, 255, 255, 0.5);
  }
  
  .followUpTextarea {
    border-color: var(--border-secondary);
  }
  
  .sendButton {
    border: 2px solid transparent;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .modal::before,
  .overlay,
  .modal,
  .closeButton,
  .sendButton,
  .closeButton::before,
  .sendButton::before,
  .loadingSpinner {
    animation: none;
    transition: none;
  }
  
  .closeButton::before,
  .sendButton::before {
    display: none;
  }
}
