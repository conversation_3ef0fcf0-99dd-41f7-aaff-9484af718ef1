@use 'variables' as *;

.quoteContainer {
  margin-top: var(--spacing-3xl);
  opacity: 0;
  transform: translateY(30px) scale(0.98);
  animation: slideUpQuote 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;

  &.showComparison {
    animation-delay: 0.2s;
  }
}

.generateSection {
  @include glass-effect(0.05, 15px);
  @include glass-border(0.15);
  border-radius: var(--radius-xl);
  padding: var(--spacing-4xl);
  text-align: center;
  background: var(--bg-tertiary);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(14, 165, 233, 0.1) 0%,
      rgba(58, 134, 255, 0.1) 50%,
      rgba(6, 255, 165, 0.1) 100%
    );
    border-radius: var(--radius-xl);
    pointer-events: none;
  }
}

.autoGeneratingIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);

  .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-light);
    border-top: 2px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

.generateHeader {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  position: relative;
  z-index: 2;

  h3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
  }

  svg {
    color: var(--color-secondary);
  }
}

.generateDescription {
  font-size: var(--font-size-lg);
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-2xl);
  line-height: var(--line-height-normal);
  position: relative;
  z-index: 2;
}

.generateButton {
  position: relative;
  z-index: 2;
  min-width: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid var(--text-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.quoteHeader {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: var(--spacing-3xl);
  flex-wrap: wrap;
  gap: var(--spacing-xl);
  padding: var(--spacing-2xl);
  @include glass-effect(0.05, 15px);
  @include glass-border(0.15);
  border-radius: var(--radius-xl);
  background: var(--bg-tertiary);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(14, 165, 233, 0.05) 0%,
      rgba(58, 134, 255, 0.05) 50%,
      rgba(6, 255, 165, 0.05) 100%
    );
    border-radius: var(--radius-xl);
    pointer-events: none;
  }

  @include responsive('tablet') {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
    padding: var(--spacing-xl);
  }

  @include responsive('mobile') {
    padding: var(--spacing-lg);
  }
}

.headerContent {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  position: relative;
  z-index: 1;

  h3 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary), var(--color-accent-blue));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0;
    line-height: 1.2;

    @include responsive('tablet') {
      font-size: var(--font-size-2xl);
    }

    @include responsive('mobile') {
      font-size: var(--font-size-xl);
    }
  }
}

.bestOptionReason {
  font-size: var(--font-size-lg);
  color: var(--color-success);
  font-weight: var(--font-weight-medium);
  margin: 0;
  padding: var(--spacing-md) var(--spacing-lg);
  background: rgba(34, 197, 94, 0.1);
  border-radius: var(--radius-md);
  border: 1px solid rgba(34, 197, 94, 0.2);
  @include glass-effect(0.05, 10px);
}

.bestBadge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: linear-gradient(135deg, var(--color-success), #059669);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  margin-left: var(--spacing-sm);
  box-shadow: var(--shadow-md);
  animation: pulse 2s infinite;
}

.projectInfo {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;

  @include responsive('tablet') {
    justify-content: center;
    gap: var(--spacing-md);
  }

  @include responsive('mobile') {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}

.projectType {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: white;
  background: linear-gradient(135deg, var(--color-secondary), var(--color-accent-blue));
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: 15px;
  border: 1px solid rgba(14, 165, 233, 0.3);
  @include glass-effect(0.1, 10px);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.2);
  text-transform: capitalize;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.complexity {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: 15px;
  border: 1px solid var(--border-secondary);
  @include glass-effect(0.05, 8px);
  text-transform: uppercase;
  letter-spacing: 1px;
  white-space: nowrap;
}

.confidence {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: white;
  background: linear-gradient(135deg, var(--color-success), #059669);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: 15px;
  border: 1px solid rgba(34, 197, 94, 0.3);
  @include glass-effect(0.1, 10px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
  white-space: nowrap;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
  position: relative;
  z-index: 1;

  @include responsive('tablet') {
    justify-content: center;
    width: 100%;
  }

  @include responsive('mobile') {
    flex-direction: column;
    gap: var(--spacing-md);
  }
}

.actionButtons {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);

  @include responsive('mobile') {
    flex-direction: column;
    width: 100%;
  }
}

.regenerateButton {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  min-width: 140px;
  justify-content: center;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: 15px;
  font-weight: var(--font-weight-semibold);
  transition: var(--transition-normal);

  &:hover {
    @include hover-lift(-2px);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }

  @include responsive('mobile') {
    width: 100%;
  }
}

.exportButton {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  min-width: 140px;
  justify-content: center;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: 15px;
  font-weight: var(--font-weight-semibold);
  transition: var(--transition-normal);

  &:hover {
    @include hover-lift(-2px);
  }

  @include responsive('mobile') {
    width: 100%;
  }
}

.savingsBadge {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: linear-gradient(135deg, var(--color-success), #059669);
  color: white;
  padding: var(--spacing-lg) var(--spacing-2xl);
  border-radius: 15px;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  box-shadow:
    0 8px 24px rgba(34, 197, 94, 0.3),
    0 4px 12px rgba(16, 185, 129, 0.2);
  @include glass-effect(0.1, 10px);
  border: 1px solid rgba(34, 197, 94, 0.3);
  position: relative;
  overflow: hidden;
  @include shimmer-effect();

  &::before {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
  }

  svg {
    flex-shrink: 0;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  }

  @include responsive('mobile') {
    width: 100%;
    justify-content: center;
    font-size: var(--font-size-base);
    padding: var(--spacing-md) var(--spacing-xl);
  }
}

.timelineComparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-3xl);
  padding: var(--spacing-2xl);
  @include glass-effect(0.08, 20px);
  @include glass-border(0.2);
  border-radius: 15px;
  background: linear-gradient(
    135deg,
    rgba(14, 165, 233, 0.05) 0%,
    rgba(58, 134, 255, 0.05) 50%,
    rgba(6, 255, 165, 0.05) 100%
  );
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 100%;
    background: linear-gradient(
      180deg,
      transparent 0%,
      var(--border-secondary) 20%,
      var(--border-secondary) 80%,
      transparent 100%
    );
    z-index: 1;

    @include responsive('tablet') {
      display: none;
    }
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at 25% 25%,
      rgba(14, 165, 233, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(58, 134, 255, 0.1) 0%,
      transparent 50%
    );
    border-radius: 15px;
    pointer-events: none;
    z-index: 0;
  }

  @include responsive('tablet') {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
    text-align: center;
    padding: var(--spacing-xl);
  }

  @include responsive('mobile') {
    padding: var(--spacing-lg);
    gap: var(--spacing-lg);
  }
}

.timelineItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  @include glass-effect(0.05, 10px);
  border-radius: 15px;
  border: 1px solid var(--border-primary);
  position: relative;
  z-index: 2;
  transition: var(--transition-normal);

  &:hover {
    @include hover-lift(-2px);
    @include glass-effect(0.1);
    border-color: var(--border-secondary);
  }

  &:first-child {
    .timelineValue {
      color: var(--text-tertiary);
    }
  }

  &:last-child {
    .timelineValue {
      background: linear-gradient(135deg, var(--color-secondary), var(--color-accent-blue));
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  @include responsive('mobile') {
    padding: var(--spacing-lg);
    gap: var(--spacing-md);
  }
}

.timelineLabel {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  text-align: center;
  letter-spacing: 0.5px;

  @include responsive('mobile') {
    font-size: var(--font-size-base);
  }
}

.timelineValue {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  text-align: center;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  @include responsive('tablet') {
    font-size: var(--font-size-3xl);
  }

  @include responsive('mobile') {
    font-size: var(--font-size-2xl);
  }
}

.comparisonGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-4xl);

  @include responsive('tablet') {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }
}

.quoteCard {
  @include glass-effect(0.05, 15px);
  @include glass-border(0.15);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  background: var(--bg-tertiary);
  position: relative;
  overflow: hidden;
  transition: var(--transition-normal);

  &:hover {
    @include hover-lift(-4px);
    @include glass-border(0.25);
  }

  &.aiDriven {
    background: linear-gradient(
      135deg,
      rgba(14, 165, 233, 0.1) 0%,
      rgba(58, 134, 255, 0.1) 100%
    );
    border-color: rgba(14, 165, 233, 0.3);

    .cardHeader h4 {
      background: linear-gradient(135deg, var(--color-secondary), var(--color-accent-blue));
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .totalCost {
      color: var(--color-secondary);
    }
  }

  &.bestOption {
    border: 2px solid var(--color-success);
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
    background: linear-gradient(
      135deg,
      rgba(34, 197, 94, 0.1) 0%,
      rgba(14, 165, 233, 0.1) 100%
    );

    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(135deg, var(--color-success), var(--color-secondary));
      border-radius: var(--radius-xl);
      z-index: -1;
      opacity: 0.8;
    }
  }
}

.cardHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-2xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-primary);

  h4 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
  }

  @include responsive('mobile') {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }
}

.totalCost {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  text-align: right;

  @include responsive('mobile') {
    text-align: center;
  }
}

.breakdown {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.breakdownItem {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  background: rgba(255, 255, 255, 0.02);
  transition: var(--transition-fast);

  &:hover {
    background: rgba(255, 255, 255, 0.05);
  }
}

.itemIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  flex-shrink: 0;
}

.itemDetails {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.itemLabel {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  margin-bottom: 2px;
}

.itemValue {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.itemCost {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  text-align: right;
  flex-shrink: 0;
}

.summarySection {
  margin-top: var(--spacing-4xl);
}

.summaryCard {
  @include glass-effect(0.05, 15px);
  @include glass-border(0.15);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  background: var(--bg-tertiary);

  h4 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-lg) 0;
  }
}

.benefitsList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);

  li {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    font-size: var(--font-size-md);
    color: var(--text-tertiary);
    line-height: var(--line-height-normal);

    &::before {
      content: '✓';
      color: var(--color-success);
      font-weight: var(--font-weight-semibold);
      flex-shrink: 0;
      margin-top: 2px;
    }
  }
}

.showComparison {
  .comparisonGrid {
    animation: slideInComparison 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .quoteCard:first-child {
    animation-delay: 0.1s;
  }

  .quoteCard:last-child {
    animation-delay: 0.2s;
  }

  .summarySection {
    animation: slideUpQuote 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
  }
}

/* Animations */
@keyframes slideUpQuote {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInComparison {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Responsive adjustments */
@include responsive('tablet') {
  .quoteContainer {
    margin-top: var(--spacing-xl);
  }

  .generateSection {
    padding: var(--spacing-2xl);
  }

  .quoteCard {
    padding: var(--spacing-xl);
  }

  .breakdownItem {
    padding: var(--spacing-sm);
  }

  .itemIcon {
    width: 28px;
    height: 28px;
  }
}

@include responsive('mobile') {
  .generateHeader {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .savingsBadge {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-md);
  }

  .totalCost {
    font-size: var(--font-size-2xl);
  }

  .breakdownItem {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .itemCost {
    text-align: left;
    align-self: flex-end;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .quoteCard {
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(0, 0, 0, 0.9);
  }
  
  .savingsBadge {
    background: var(--color-success);
    color: white;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .quoteContainer,
  .comparisonGrid,
  .summarySection,
  .quoteCard,
  .spinner {
    animation: none;
    transition: none;
  }
  
  .quoteCard:hover {
    transform: none;
  }
}
