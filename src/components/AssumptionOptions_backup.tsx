import React from 'react';
import { Lightbulb, HelpCircle } from 'lucide-react';
import buttonStyles from '@/styles/buttons.module.scss';
import layoutStyles from '@/styles/layout.module.scss';

interface AssumptionOption {
  question: string;
  assumption: string;
}

interface AssumptionOptionsProps {
  response: string;
  onAssumptionSelect: (assumption: string) => void;
  disabled?: boolean;
}

const AssumptionOptions: React.FC<AssumptionOptionsProps> = ({
  response,
  onAssumptionSelect,
  disabled = false,
}) => {
  // Parse the response to extract Q: and Assume: pairs
  const parseAssumptions = (text: string): AssumptionOption[] => {
    const options: AssumptionOption[] = [];
    const lines = text.split('\n').map(line => line.trim()).filter(line => line);
    
    let currentQuestion = '';
    let currentAssumption = '';
    
    for (const line of lines) {
      if (line.startsWith('Q:')) {
        // If we have a previous complete pair, save it
        if (currentQuestion && currentAssumption) {
          options.push({
            question: currentQuestion,
            assumption: currentAssumption
          });
        }
        currentQuestion = line.substring(2).trim();
        currentAssumption = '';
      } else if (line.startsWith('Assume:')) {
        currentAssumption = line.substring(7).trim();
      }
    }
    
    // Add the last pair if complete
    if (currentQuestion && currentAssumption) {
      options.push({
        question: currentQuestion,
        assumption: currentAssumption
      });
    }
    
    return options;
  };

  const assumptionOptions = parseAssumptions(response);

  // If no Q:/Assume: pairs found, don't render anything
  if (assumptionOptions.length === 0) {
    return null;
  }

  return (
    <div className={layoutStyles.assumptionOptionsContainer}>
      <div className={layoutStyles.assumptionOptionsHeader}>
        <Lightbulb size={16} />
        <span>Quick Assumptions - Click to proceed faster:</span>
      </div>
      
      {assumptionOptions.map((option, index) => (
        <div key={index} className={layoutStyles.assumptionOptionCard}>
          <div className={layoutStyles.questionText}>
            <HelpCircle size={14} />
            <span>{option.question}</span>
          </div>
          
          <button
            className={`${buttonStyles.btn} ${buttonStyles.assumptionButton}`}
            onClick={() => onAssumptionSelect(option.assumption)}
            disabled={disabled}
            type="button"
          >
            <Lightbulb size={14} />
            <span>{option.assumption}</span>
          </button>
        </div>
      ))}
      
      <div className={layoutStyles.assumptionOptionsFooter}>
        Or answer the questions manually in the text box below
      </div>
    </div>
  );
};

export { AssumptionOptions };
export default AssumptionOptions;
