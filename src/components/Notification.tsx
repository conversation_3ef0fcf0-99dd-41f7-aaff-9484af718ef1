import React, { useEffect } from 'react';

interface NotificationProps {
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  visible: boolean;
  onClose: () => void;
  className?: string;
}

const Notification: React.FC<NotificationProps> = ({
  message,
  type,
  visible,
  onClose,
  className,
}) => {
  const getIcon = () => {
    switch (type) {
      case 'success':
        return '✅';
      case 'warning':
        return '⚠️';
      case 'error':
        return '❌';
      default:
        return 'ℹ️';
    }
  };

  const getBackgroundColor = () => {
    switch (type) {
      case 'success':
        return '#10b981';
      case 'warning':
        return '#f59e0b';
      case 'error':
        return '#ef4444';
      default:
        return '#3b82f6';
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <div
      className={`notification ${className || ''}`}
      style={{
        background: getBackgroundColor(),
        color: 'white',
        padding: '12px 16px',
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        minWidth: '300px',
        maxWidth: '500px',
        pointerEvents: 'auto',
        transform: visible ? 'translateY(0)' : 'translateY(-20px)',
        opacity: visible ? 1 : 0,
        transition: 'all 0.3s ease',
        backdropFilter: 'blur(4px)',
      }}
      role="alert"
      aria-live="polite"
      tabIndex={0}
      onKeyDown={handleKeyDown}
    >
      <span aria-hidden="true">{getIcon()}</span>
      <span style={{ flex: 1, fontSize: '14px', fontWeight: '500' }}>
        {message}
      </span>
      <button
        type="button"
        onClick={onClose}
        style={{
          background: 'rgba(255, 255, 255, 0.2)',
          border: 'none',
          borderRadius: '4px',
          color: 'white',
          cursor: 'pointer',
          padding: '4px 8px',
          fontSize: '12px',
          fontWeight: 'bold',
          transition: 'background 0.2s ease',
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.3)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';
        }}
        aria-label="Close notification"
      >
        ✕
      </button>
    </div>
  );
};

export { Notification };
export default Notification;
