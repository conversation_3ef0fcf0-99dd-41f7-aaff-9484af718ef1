import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Bot, ImageIcon } from 'lucide-react';
import { apiService } from '@/services/api';
import { MESSAGES, LABELS, PLACEHOLDERS, VALIDATION_MESSAGES } from '@/constants';
import { QuoteGenerator } from './QuoteGenerator';
import { AssumptionOptions } from './AssumptionOptions';
import type { PopupState, FollowUpData, FollowUpMessage, QuoteData } from '@/types';
import popupStyles from '@/styles/popup.module.scss';

interface ResponsePopupProps {
  popupState: PopupState;
  onClose: () => void;
  onNotification: (message: string, type: 'info' | 'success' | 'warning' | 'error') => void;
  onFollowUpAdded: (conversationId: string, question: string, response: string) => void;
  projectDescription?: string;
  projectFormat?: string;
  assumptionMode?: boolean;
  autoQuoteData?: QuoteData;
  onRequestNewQuote?: () => void;
  onQuoteGenerated?: (quote: QuoteData) => void;
}

const ResponsePopup: React.FC<ResponsePopupProps> = ({
  popupState,
  onClose,
  onNotification,
  onFollowUpAdded,
  projectDescription = '',
  projectFormat = 'web',
  assumptionMode = false,
  autoQuoteData,
  onRequestNewQuote,
  onQuoteGenerated,
}) => {
  const [followUpMessage, setFollowUpMessage] = useState('');
  const [isLoadingFollowUp, setIsLoadingFollowUp] = useState(false);
  const [followUpHistory, setFollowUpHistory] = useState<FollowUpMessage[]>(popupState.followUpHistory);
  const overlayRef = useRef<HTMLDivElement>(null);
  const followUpTextareaRef = useRef<HTMLTextAreaElement>(null);

  // Handle escape key to close popup
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (popupState.isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when popup is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [popupState.isOpen, onClose]);

  // Handle click outside to close popup
  const handleOverlayClick = useCallback((event: React.MouseEvent) => {
    if (event.target === overlayRef.current) {
      onClose();
    }
  }, [onClose]);

  // Handle assumption selection - auto-send like typing and submitting
  const handleAssumptionSelect = useCallback(async (assumption: string) => {
    if (isLoadingFollowUp) return;

    const followUpData: FollowUpData = {
      message: assumption,
      conversationId: popupState.conversationId,
      assumptionMode: true, // Force assumption mode for this request
    };

    setIsLoadingFollowUp(true);

    try {
      const response = await apiService.sendFollowUp(followUpData);

      const newFollowUp: FollowUpMessage = {
        id: Date.now().toString(),
        question: assumption, // Use the assumption directly as the question
        response: response.response,
        timestamp: new Date(),
      };

      setFollowUpHistory(prev => [...prev, newFollowUp]);
      
      // Notify parent component to update chat history
      onFollowUpAdded(popupState.conversationId!, assumption, response.response);
      
      onNotification('Assumption selected successfully!', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : VALIDATION_MESSAGES.FOLLOWUP_SEND_FAILED;
      onNotification(errorMessage, 'error');
    } finally {
      setIsLoadingFollowUp(false);
    }
  }, [isLoadingFollowUp, popupState.conversationId, onNotification, onFollowUpAdded]);

  // Handle follow-up submission
  const handleFollowUpSubmit = useCallback(async () => {
    if (!followUpMessage.trim() || isLoadingFollowUp) return;

    const followUpData: FollowUpData = {
      message: followUpMessage.trim(),
      conversationId: popupState.conversationId,
      assumptionMode: assumptionMode,
    };

    setIsLoadingFollowUp(true);

    try {
      // Use the dedicated follow-up API endpoint
      const response = await apiService.sendFollowUp(followUpData);

      const newFollowUp: FollowUpMessage = {
        id: Date.now().toString(),
        question: followUpMessage.trim(),
        response: response.response,
        timestamp: new Date(),
      };

      setFollowUpHistory(prev => [...prev, newFollowUp]);
      setFollowUpMessage('');
      
      // Notify parent component to update chat history
      onFollowUpAdded(popupState.conversationId!, followUpMessage.trim(), response.response);
      
      onNotification(MESSAGES.SUCCESS_FOLLOWUP_RECEIVED, 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : VALIDATION_MESSAGES.FOLLOWUP_SEND_FAILED;
      onNotification(errorMessage, 'error');
    } finally {
      setIsLoadingFollowUp(false);
    }
  }, [followUpMessage, isLoadingFollowUp, popupState.conversationId, onNotification, onFollowUpAdded, assumptionMode]);

  // Handle Enter key in textarea
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      handleFollowUpSubmit();
    }
  }, [handleFollowUpSubmit]);

  // Sync followUpHistory when popupState changes
  useEffect(() => {
    setFollowUpHistory(popupState.followUpHistory);
  }, [popupState.followUpHistory]);

  // Get the latest response text to display (same logic as AssumptionOptions)
  const getLatestResponseText = (): string => {
    // If there are follow-up responses, use the latest one
    if (followUpHistory.length > 0) {
      const latestFollowUp = followUpHistory[followUpHistory.length - 1];
      return latestFollowUp.response.replace(/Assume:.+/g, '');
    }

    // Otherwise, use the original response
    return popupState.response;
  };

  // Focus textarea when popup opens
  useEffect(() => {
    if (popupState.isOpen && followUpTextareaRef.current) {
      // Small delay to ensure popup animation completes
      setTimeout(() => {
        followUpTextareaRef.current?.focus();
      }, 300);
    }
  }, [popupState.isOpen]);

  if (!popupState.isOpen || !popupState.response) {
    return null;
  }

  const isError = popupState.response.includes('❌ Error:');

  return (
    <div 
      ref={overlayRef}
      className={popupStyles.overlay} 
      onClick={handleOverlayClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="popup-title"
    >
      <div className={popupStyles.modal}>
        <div className={popupStyles.header}>
          <h2 id="popup-title" className={popupStyles.title}>
            <Bot size={20} style={{ marginRight: '8px' }} />
            {MESSAGES.POPUP_TITLE}
          </h2>
          <button
            className={popupStyles.closeButton}
            onClick={onClose}
            aria-label={LABELS.ARIA_CLOSE_POPUP}
            type="button"
          >
            {LABELS.CLOSE}
          </button>
        </div>

        <div className={popupStyles.content}>
          {popupState.imageAnalyzed && (
            <div className={popupStyles.imageAnalyzedBadge}>
              <ImageIcon size={16} style={{ marginRight: '6px' }} />
              {MESSAGES.IMAGE_ANALYZED_BADGE}
            </div>
          )}

          <div
            className={`${popupStyles.responseContent} ${isError ? popupStyles.errorMessage : ''}`}
          >
            {getLatestResponseText()}
          </div>

          {/* Follow-up History - Hidden per user request */}
          {/* {followUpHistory.length > 0 && (
            <div className={popupStyles.followUpHistory}>
              {followUpHistory.map((followUp) => (
                <div key={followUp.id} className={popupStyles.followUpItem}>
                  <div className={popupStyles.followUpQuestion}>
                    {MESSAGES.FOLLOWUP_QUESTION_PREFIX} {followUp.question}
                  </div>
                  <div className={popupStyles.followUpResponse}>
                    {MESSAGES.FOLLOWUP_RESPONSE_PREFIX} {followUp.response}
                  </div>
                </div>
              ))}
            </div>
          )} */}

          {/* Follow-up Input Section */}
          <div className={popupStyles.followUpSection}>
            {/* Assumption Options - Show when response contains Assume: statements */}
            {!isError && (
              <AssumptionOptions
                response={popupState.response}
                followUpHistory={followUpHistory}
                onAssumptionSelect={handleAssumptionSelect}
                disabled={isLoadingFollowUp}
              />
            )}

            <div className={popupStyles.followUpInput}>
              <textarea
                ref={followUpTextareaRef}
                value={followUpMessage}
                onChange={(e) => setFollowUpMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                className={popupStyles.followUpTextarea}
                placeholder={PLACEHOLDERS.FOLLOWUP_QUESTION}
                disabled={isLoadingFollowUp}
                rows={3}
                maxLength={1000}
              />
              <button
                onClick={handleFollowUpSubmit}
                disabled={!followUpMessage.trim() || isLoadingFollowUp}
                className={popupStyles.sendButton}
                type="button"
              >
                {isLoadingFollowUp && (
                  <span className={popupStyles.loadingSpinner} />
                )}
                {isLoadingFollowUp ? LABELS.SENDING : LABELS.SEND}
              </button>
            </div>
          </div>

          {/* Quote Generator - Show when response is comprehensive, not an error, or when should show end prompt */}
          {!isError && (projectDescription || autoQuoteData || popupState.shouldShowEndPrompt) && (
            <QuoteGenerator
              projectDescription={projectDescription}
              projectFormat={projectFormat}
              autoGeneratedQuote={autoQuoteData}
              autoGenerate={popupState.shouldShowEndPrompt && !autoQuoteData}
              onQuoteGenerated={onQuoteGenerated}
            />
          )}


          {/* End Prompt CTA - Show after quote generation */}
          {!isError && projectDescription && popupState.shouldShowEndPrompt && (
            <div className={popupStyles.endPromptSection}>
              <div className={popupStyles.ctaCard}>
                <h3>Ready to Move Forward?</h3>
                <p>
                  You now have detailed quotes for both traditional and AI-driven development approaches.
                  Take these estimates to discuss your project requirements and next steps.
                </p>
                <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
                  <button
                    className={popupStyles.ctaButton}
                    onClick={() => {
                      onNotification('Contact B&T to discuss your project quotes and next steps!', 'info');
                    }}
                  >
                    Take This Quote to B&T
                  </button>
                  {onRequestNewQuote && (
                    <button
                      className={`${popupStyles.ctaButton} ${popupStyles.secondaryButton}`}
                      onClick={onRequestNewQuote}
                      style={{
                        backgroundColor: '#f8f9fa',
                        color: '#495057',
                        border: '2px solid #dee2e6'
                      }}
                    >
                      Request Another Quote
                    </button>
                  )}
                </div>
                <p className={popupStyles.ctaSubtext}>
                  Our team will help you choose the best development approach for your project.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export { ResponsePopup };
export default ResponsePopup;
