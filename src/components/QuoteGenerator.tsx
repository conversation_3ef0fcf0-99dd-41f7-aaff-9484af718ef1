import React, { useState, useEffect } from 'react';
import { Calculator, TrendingDown, Clock, Users, Palette, Bug, Download, Server, Shield, Wrench, RefreshCw } from 'lucide-react';
import layoutStyles from '@/styles/layout.module.scss';
import buttonStyles from '@/styles/buttons.module.scss';
import quoteStyles from '@/styles/quote.module.scss';

interface ProjectEstimate {
  developmentHours: number;
  projectManagementHours: number;
  designHours: number;
  qaHours: number;
  infrastructureHours: number;
  maintenanceHours: number;
  hourlyRate: number;
  complexity: 'small' | 'medium' | 'large';
  projectType: string;
  riskFactor: number;
}

interface QuoteData {
  traditional: ProjectEstimate & { total: number; breakdown: CostBreakdown };
  aiDriven: ProjectEstimate & { total: number; breakdown: CostBreakdown };
  savings: {
    amount: number;
    percentage: number;
  };
  timeline: {
    traditional: number;
    aiDriven: number;
  };
  confidence: number;
}

interface CostBreakdown {
  development: number;
  projectManagement: number;
  design: number;
  qa: number;
  infrastructure: number;
  maintenance: number;
}

interface QuoteGeneratorProps {
  projectDescription: string;
  projectFormat: string;
  autoGeneratedQuote?: QuoteData;
  autoGenerate?: boolean;
  onQuoteGenerated?: (quote: QuoteData) => void;
}

const QuoteGenerator: React.FC<QuoteGeneratorProps> = ({
  projectDescription,
  projectFormat,
  autoGeneratedQuote,
  autoGenerate = false,
  onQuoteGenerated
}) => {
  const [quote, setQuote] = useState<QuoteData | null>(autoGeneratedQuote || null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showComparison, setShowComparison] = useState(!!autoGeneratedQuote);

  const handleGenerateQuote = async () => {
    setIsGenerating(true);

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    const generatedQuote = generateEstimate(projectDescription, projectFormat);
    setQuote(generatedQuote);
    setIsGenerating(false);
    setShowComparison(true);

    if (onQuoteGenerated) {
      onQuoteGenerated(generatedQuote);
    }
  };

  // Handle auto-generated quote - initialize and update when it changes
  useEffect(() => {
    if (autoGeneratedQuote) {
      setQuote(autoGeneratedQuote);
      setShowComparison(true);
    }
  }, [autoGeneratedQuote]);

  // Auto-generate quote when autoGenerate is true and no existing quote
  useEffect(() => {
    if (autoGenerate && !quote && !autoGeneratedQuote && !isGenerating && projectDescription) {
      handleGenerateQuote();
    }
  }, [autoGenerate, quote, autoGeneratedQuote, isGenerating, projectDescription]);

  // Skip the generate button when auto-generating or when we have an existing quote
  const shouldShowGenerateButton = !autoGenerate && !quote && !autoGeneratedQuote && !isGenerating;

  const generateEstimate = (description: string, format: string): QuoteData => {
    // Analyze project complexity and characteristics
    const complexity = analyzeComplexity(description);
    const projectType = analyzeProjectType(description, format);
    const riskFactor = analyzeRiskFactor(description, format);
    const hourlyRate = 150; // Standard rate

    // Base estimates for traditional development
    const baseEstimates = getBaseEstimates(complexity, format);
    
    // Calculate traditional costs with enhanced breakdown
    const traditionalTotalHours = baseEstimates.developmentHours + baseEstimates.projectManagementHours + 
                                 baseEstimates.designHours + baseEstimates.qaHours + 
                                 baseEstimates.infrastructureHours + baseEstimates.maintenanceHours;
    
    const traditional = {
      ...baseEstimates,
      hourlyRate,
      complexity,
      projectType,
      riskFactor,
      total: Math.round(traditionalTotalHours * hourlyRate * (1 + riskFactor)),
      breakdown: {
        development: Math.round(baseEstimates.developmentHours * hourlyRate * (1 + riskFactor)),
        projectManagement: Math.round(baseEstimates.projectManagementHours * hourlyRate * (1 + riskFactor)),
        design: Math.round(baseEstimates.designHours * hourlyRate * (1 + riskFactor)),
        qa: Math.round(baseEstimates.qaHours * hourlyRate * (1 + riskFactor)),
        infrastructure: Math.round(baseEstimates.infrastructureHours * hourlyRate * (1 + riskFactor)),
        maintenance: Math.round(baseEstimates.maintenanceHours * hourlyRate * (1 + riskFactor))
      }
    };

    // Calculate AI-driven savings with more sophisticated logic
    const savingsPercentage = complexity === 'large' ? 0.25 : 0.40;
    const aiEfficiencyFactors = {
      development: savingsPercentage,
      projectManagement: savingsPercentage * 0.5,
      design: savingsPercentage * 0.3,
      qa: savingsPercentage * 0.6,
      infrastructure: savingsPercentage * 0.4,
      maintenance: savingsPercentage * 0.7
    };

    const aiDriven = {
      developmentHours: Math.round(baseEstimates.developmentHours * (1 - aiEfficiencyFactors.development)),
      projectManagementHours: Math.round(baseEstimates.projectManagementHours * (1 - aiEfficiencyFactors.projectManagement)),
      designHours: Math.round(baseEstimates.designHours * (1 - aiEfficiencyFactors.design)),
      qaHours: Math.round(baseEstimates.qaHours * (1 - aiEfficiencyFactors.qa)),
      infrastructureHours: Math.round(baseEstimates.infrastructureHours * (1 - aiEfficiencyFactors.infrastructure)),
      maintenanceHours: Math.round(baseEstimates.maintenanceHours * (1 - aiEfficiencyFactors.maintenance)),
      hourlyRate,
      complexity,
      projectType,
      riskFactor: riskFactor * 0.6, // AI reduces project risk
      total: 0,
      breakdown: {
        development: 0,
        projectManagement: 0,
        design: 0,
        qa: 0,
        infrastructure: 0,
        maintenance: 0
      }
    };

    // Calculate AI-driven breakdown
    aiDriven.breakdown.development = Math.round(aiDriven.developmentHours * hourlyRate * (1 + aiDriven.riskFactor));
    aiDriven.breakdown.projectManagement = Math.round(aiDriven.projectManagementHours * hourlyRate * (1 + aiDriven.riskFactor));
    aiDriven.breakdown.design = Math.round(aiDriven.designHours * hourlyRate * (1 + aiDriven.riskFactor));
    aiDriven.breakdown.qa = Math.round(aiDriven.qaHours * hourlyRate * (1 + aiDriven.riskFactor));
    aiDriven.breakdown.infrastructure = Math.round(aiDriven.infrastructureHours * hourlyRate * (1 + aiDriven.riskFactor));
    aiDriven.breakdown.maintenance = Math.round(aiDriven.maintenanceHours * hourlyRate * (1 + aiDriven.riskFactor));
    
    aiDriven.total = Object.values(aiDriven.breakdown).reduce((sum, cost) => sum + cost, 0);

    // Ensure no quote exceeds $85,000
    if (traditional.total > 85000) {
      const scaleFactor = 85000 / traditional.total;
      
      // Scale traditional estimate
      Object.keys(traditional.breakdown).forEach(key => {
        traditional.breakdown[key as keyof CostBreakdown] = Math.round(traditional.breakdown[key as keyof CostBreakdown] * scaleFactor);
      });
      traditional.total = 85000;
      
      // Recalculate AI-driven based on scaled traditional
      const newAiTotal = Math.round(traditional.total * (1 - savingsPercentage));
      const aiScaleFactor = newAiTotal / aiDriven.total;
      
      Object.keys(aiDriven.breakdown).forEach(key => {
        aiDriven.breakdown[key as keyof CostBreakdown] = Math.round(aiDriven.breakdown[key as keyof CostBreakdown] * aiScaleFactor);
      });
      aiDriven.total = newAiTotal;
    }

    const savings = {
      amount: traditional.total - aiDriven.total,
      percentage: Math.round(((traditional.total - aiDriven.total) / traditional.total) * 100)
    };

    // Calculate timeline estimates (in weeks)
    const timeline = {
      traditional: Math.ceil(traditionalTotalHours / 40), // 40 hours per week
      aiDriven: Math.ceil((aiDriven.developmentHours + aiDriven.projectManagementHours + 
                          aiDriven.designHours + aiDriven.qaHours + 
                          aiDriven.infrastructureHours + aiDriven.maintenanceHours) / 40)
    };

    // Calculate confidence level based on project characteristics
    const confidence = calculateConfidence(complexity, projectType, riskFactor);

    return { traditional, aiDriven, savings, timeline, confidence };
  };

  const analyzeComplexity = (description: string): 'small' | 'medium' | 'large' => {
    const complexityIndicators = {
      small: ['simple', 'basic', 'minimal', 'single page', 'landing page', 'portfolio', 'blog'],
      medium: ['dashboard', 'user management', 'database', 'api', 'authentication', 'e-commerce', 'cms'],
      large: ['enterprise', 'complex', 'multiple systems', 'integration', 'scalable', 'microservices', 'real-time', 'blockchain']
    };

    const lowerDesc = description.toLowerCase();
    
    if (complexityIndicators.large.some(indicator => lowerDesc.includes(indicator))) {
      return 'large';
    }
    if (complexityIndicators.medium.some(indicator => lowerDesc.includes(indicator))) {
      return 'medium';
    }
    return 'small';
  };

  const analyzeProjectType = (description: string, format: string): string => {
    const typeIndicators = {
      'E-commerce': ['shop', 'store', 'cart', 'payment', 'checkout', 'product'],
      'SaaS Platform': ['subscription', 'tenant', 'billing', 'analytics', 'dashboard'],
      'Mobile App': ['mobile', 'ios', 'android', 'app store'],
      'Enterprise System': ['enterprise', 'erp', 'crm', 'workflow', 'automation'],
      'Content Management': ['cms', 'blog', 'content', 'publishing', 'editorial'],
      'Real-time Application': ['real-time', 'chat', 'messaging', 'live', 'streaming'],
      'API/Backend': ['api', 'backend', 'microservice', 'rest', 'graphql'],
      'Web Application': ['web', 'webapp', 'browser', 'responsive']
    };

    const lowerDesc = description.toLowerCase();
    
    for (const [type, indicators] of Object.entries(typeIndicators)) {
      if (indicators.some(indicator => lowerDesc.includes(indicator))) {
        return type;
      }
    }
    
    return format.charAt(0).toUpperCase() + format.slice(1).replace('-', ' ');
  };

  const analyzeRiskFactor = (description: string, format: string): number => {
    let riskFactor = 0.1; // Base risk
    
    const highRiskIndicators = ['integration', 'legacy', 'migration', 'complex', 'real-time', 'blockchain'];
    const mediumRiskIndicators = ['authentication', 'payment', 'security', 'scalable', 'performance'];
    const lowRiskIndicators = ['simple', 'basic', 'standard', 'template'];
    
    const lowerDesc = description.toLowerCase();
    
    if (highRiskIndicators.some(indicator => lowerDesc.includes(indicator))) {
      riskFactor += 0.2;
    }
    if (mediumRiskIndicators.some(indicator => lowerDesc.includes(indicator))) {
      riskFactor += 0.1;
    }
    if (lowRiskIndicators.some(indicator => lowerDesc.includes(indicator))) {
      riskFactor -= 0.05;
    }
    
    return Math.max(0.05, Math.min(0.3, riskFactor)); // Cap between 5% and 30%
  };

  const calculateConfidence = (complexity: string, projectType: string, riskFactor: number): number => {
    let confidence = 85; // Base confidence
    
    // Adjust based on complexity
    if (complexity === 'small') confidence += 10;
    else if (complexity === 'large') confidence -= 10;
    
    // Adjust based on risk
    confidence -= Math.round(riskFactor * 100);
    
    // Adjust based on project type familiarity
    const familiarTypes = ['Web Application', 'API/Backend', 'Content Management'];
    if (familiarTypes.includes(projectType)) confidence += 5;
    
    return Math.max(70, Math.min(95, confidence)); // Cap between 70% and 95%
  };

  const getBaseEstimates = (complexity: 'small' | 'medium' | 'large', format: string): Omit<ProjectEstimate, 'hourlyRate' | 'complexity' | 'projectType' | 'riskFactor'> => {
    const baseHours = {
      small: { dev: 80, pm: 20, design: 30, qa: 25, infra: 15, maintenance: 10 },
      medium: { dev: 200, pm: 50, design: 60, qa: 50, infra: 30, maintenance: 25 },
      large: { dev: 400, pm: 100, design: 120, qa: 100, infra: 60, maintenance: 50 }
    };

    const formatMultipliers = {
      'web': 1.0,
      'webapp': 1.1,
      'backend-api': 0.8,
      'database-integration': 1.4,
      'model-training': 1.6,
      'hybrid-webapp': 1.3,
      'hybrid-platform': 1.4,
      'microservices': 1.5,
      'cloud-native': 1.3,
      'real-time-app': 1.4,
      'blockchain-app': 1.8,
      'iot-integration': 1.6,
      'ai-ml-integration': 1.7,
      'enterprise-system': 1.5
    };

    const multiplier = formatMultipliers[format as keyof typeof formatMultipliers] || 1.0;
    const base = baseHours[complexity];

    return {
      developmentHours: Math.round(base.dev * multiplier),
      projectManagementHours: Math.round(base.pm * multiplier),
      designHours: Math.round(base.design * multiplier),
      qaHours: Math.round(base.qa * multiplier),
      infrastructureHours: Math.round(base.infra * multiplier),
      maintenanceHours: Math.round(base.maintenance * multiplier)
    };
  };



  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatHours = (hours: number) => {
    return `${hours}h`;
  };

  const formatWeeks = (weeks: number) => {
    return weeks === 1 ? '1 week' : `${weeks} weeks`;
  };

  const exportQuote = () => {
    if (!quote) return;
    
    const quoteData = {
      projectType: quote.traditional.projectType,
      complexity: quote.traditional.complexity,
      confidence: `${quote.confidence}%`,
      traditional: {
        total: formatCurrency(quote.traditional.total),
        timeline: formatWeeks(quote.timeline.traditional),
        breakdown: {
          development: formatCurrency(quote.traditional.breakdown.development),
          projectManagement: formatCurrency(quote.traditional.breakdown.projectManagement),
          design: formatCurrency(quote.traditional.breakdown.design),
          qa: formatCurrency(quote.traditional.breakdown.qa),
          infrastructure: formatCurrency(quote.traditional.breakdown.infrastructure),
          maintenance: formatCurrency(quote.traditional.breakdown.maintenance)
        }
      },
      aiDriven: {
        total: formatCurrency(quote.aiDriven.total),
        timeline: formatWeeks(quote.timeline.aiDriven),
        breakdown: {
          development: formatCurrency(quote.aiDriven.breakdown.development),
          projectManagement: formatCurrency(quote.aiDriven.breakdown.projectManagement),
          design: formatCurrency(quote.aiDriven.breakdown.design),
          qa: formatCurrency(quote.aiDriven.breakdown.qa),
          infrastructure: formatCurrency(quote.aiDriven.breakdown.infrastructure),
          maintenance: formatCurrency(quote.aiDriven.breakdown.maintenance)
        }
      },
      savings: {
        amount: formatCurrency(quote.savings.amount),
        percentage: `${quote.savings.percentage}%`
      }
    };

    const dataStr = JSON.stringify(quoteData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `project-quote-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  if (!quote) {
    return (
      <div className={quoteStyles.quoteContainer}>
        {autoGenerate && isGenerating && (
          <div className={quoteStyles.generateSection}>
            <div className={quoteStyles.autoGeneratingIndicator}>
              <div className={quoteStyles.spinner} />
              <span>Generating Quote...</span>
            </div>
          </div>
        )}
        {shouldShowGenerateButton && (
          <div className={quoteStyles.generateSection}>
            <div className={quoteStyles.generateHeader}>
              <Calculator size={24} />
              <h3>Generate Project Quote</h3>
            </div>
            <p className={quoteStyles.generateDescription}>
              Get an instant cost comparison between traditional development and AI-driven development for your project.
            </p>
            <button
              onClick={handleGenerateQuote}
              disabled={isGenerating}
              className={`${buttonStyles.btnPrimary} ${quoteStyles.generateButton}`}
            >
              {isGenerating ? (
                <>
                  <div className={quoteStyles.spinner} />
                  Generating Quote...
                </>
              ) : (
                <>
                  <Calculator size={16} />
                  Generate Quote
                </>
              )}
            </button>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`${quoteStyles.quoteContainer} ${showComparison ? quoteStyles.showComparison : ''}`}>
      <div className={quoteStyles.quoteHeader}>
        <div className={quoteStyles.headerContent}>
          <h3>{(quote as any).bestOption ? 'Best Quote - AI-Driven Development' : 'Project Cost Comparison'}</h3>
          {(quote as any).bestOptionReason && (
            <p className={quoteStyles.bestOptionReason}>{(quote as any).bestOptionReason}</p>
          )}
          <div className={quoteStyles.projectInfo}>
            <span className={quoteStyles.projectType}>{quote.traditional.projectType}</span>
            <span className={quoteStyles.complexity}>{quote.traditional.complexity.toUpperCase()} Project</span>
            <span className={quoteStyles.confidence}>{quote.confidence}% Confidence</span>
          </div>
        </div>
        <div className={quoteStyles.headerActions}>
          <div className={quoteStyles.savingsBadge}>
            <TrendingDown size={16} />
            Save {formatCurrency(quote.savings.amount)} ({quote.savings.percentage}%)
          </div>
          <div className={quoteStyles.actionButtons}>
            <button
              onClick={handleGenerateQuote}
              className={`${buttonStyles.btnOutline} ${quoteStyles.regenerateButton}`}
              disabled={isGenerating}
            >
              <RefreshCw size={16} />
              {isGenerating ? 'Regenerating...' : 'Regenerate'}
            </button>
            <button
              onClick={exportQuote}
              className={`${buttonStyles.btnSecondary} ${quoteStyles.exportButton}`}
            >
              <Download size={16} />
              Export Quote
            </button>
          </div>
        </div>
      </div>

      <div className={quoteStyles.timelineComparison}>
        <div className={quoteStyles.timelineItem}>
          <span className={quoteStyles.timelineLabel}>Traditional Timeline:</span>
          <span className={quoteStyles.timelineValue}>{formatWeeks(quote.timeline.traditional)}</span>
        </div>
        <div className={quoteStyles.timelineItem}>
          <span className={quoteStyles.timelineLabel}>AI-Driven Timeline:</span>
          <span className={quoteStyles.timelineValue}>{formatWeeks(quote.timeline.aiDriven)}</span>
        </div>
      </div>

      <div className={quoteStyles.comparisonGrid}>
        {/* Traditional Development */}
        <div className={quoteStyles.quoteCard}>
          <div className={quoteStyles.cardHeader}>
            <h4>Traditional Development</h4>
            <div className={quoteStyles.totalCost}>{formatCurrency(quote.traditional.total)}</div>
          </div>
          <div className={quoteStyles.breakdown}>
            <div className={quoteStyles.breakdownItem}>
              <div className={quoteStyles.itemIcon}>
                <Clock size={16} />
              </div>
              <div className={quoteStyles.itemDetails}>
                <span className={quoteStyles.itemLabel}>Development</span>
                <span className={quoteStyles.itemValue}>{formatHours(quote.traditional.developmentHours)}</span>
              </div>
              <div className={quoteStyles.itemCost}>
                {formatCurrency(quote.traditional.breakdown.development)}
              </div>
            </div>
            <div className={quoteStyles.breakdownItem}>
              <div className={quoteStyles.itemIcon}>
                <Users size={16} />
              </div>
              <div className={quoteStyles.itemDetails}>
                <span className={quoteStyles.itemLabel}>Project Management</span>
                <span className={quoteStyles.itemValue}>{formatHours(quote.traditional.projectManagementHours)}</span>
              </div>
              <div className={quoteStyles.itemCost}>
                {formatCurrency(quote.traditional.breakdown.projectManagement)}
              </div>
            </div>
            <div className={quoteStyles.breakdownItem}>
              <div className={quoteStyles.itemIcon}>
                <Palette size={16} />
              </div>
              <div className={quoteStyles.itemDetails}>
                <span className={quoteStyles.itemLabel}>Design</span>
                <span className={quoteStyles.itemValue}>{formatHours(quote.traditional.designHours)}</span>
              </div>
              <div className={quoteStyles.itemCost}>
                {formatCurrency(quote.traditional.breakdown.design)}
              </div>
            </div>
            <div className={quoteStyles.breakdownItem}>
              <div className={quoteStyles.itemIcon}>
                <Bug size={16} />
              </div>
              <div className={quoteStyles.itemDetails}>
                <span className={quoteStyles.itemLabel}>QA Testing</span>
                <span className={quoteStyles.itemValue}>{formatHours(quote.traditional.qaHours)}</span>
              </div>
              <div className={quoteStyles.itemCost}>
                {formatCurrency(quote.traditional.breakdown.qa)}
              </div>
            </div>
            <div className={quoteStyles.breakdownItem}>
              <div className={quoteStyles.itemIcon}>
                <Server size={16} />
              </div>
              <div className={quoteStyles.itemDetails}>
                <span className={quoteStyles.itemLabel}>Infrastructure</span>
                <span className={quoteStyles.itemValue}>{formatHours(quote.traditional.infrastructureHours)}</span>
              </div>
              <div className={quoteStyles.itemCost}>
                {formatCurrency(quote.traditional.breakdown.infrastructure)}
              </div>
            </div>
            <div className={quoteStyles.breakdownItem}>
              <div className={quoteStyles.itemIcon}>
                <Wrench size={16} />
              </div>
              <div className={quoteStyles.itemDetails}>
                <span className={quoteStyles.itemLabel}>Maintenance</span>
                <span className={quoteStyles.itemValue}>{formatHours(quote.traditional.maintenanceHours)}</span>
              </div>
              <div className={quoteStyles.itemCost}>
                {formatCurrency(quote.traditional.breakdown.maintenance)}
              </div>
            </div>
          </div>
        </div>

        {/* AI-Driven Development */}
        <div className={`${quoteStyles.quoteCard} ${quoteStyles.aiDriven} ${(quote as any).bestOption === 'aiDriven' ? quoteStyles.bestOption : ''}`}>
          <div className={quoteStyles.cardHeader}>
            <h4>
              AI-Driven Development
              {(quote as any).bestOption === 'aiDriven' && (
                <span className={quoteStyles.bestBadge}>✨ BEST VALUE</span>
              )}
            </h4>
            <div className={quoteStyles.totalCost}>{formatCurrency(quote.aiDriven.total)}</div>
          </div>
          <div className={quoteStyles.breakdown}>
            <div className={quoteStyles.breakdownItem}>
              <div className={quoteStyles.itemIcon}>
                <Clock size={16} />
              </div>
              <div className={quoteStyles.itemDetails}>
                <span className={quoteStyles.itemLabel}>Development</span>
                <span className={quoteStyles.itemValue}>{formatHours(quote.aiDriven.developmentHours)}</span>
              </div>
              <div className={quoteStyles.itemCost}>
                {formatCurrency(quote.aiDriven.breakdown.development)}
              </div>
            </div>
            <div className={quoteStyles.breakdownItem}>
              <div className={quoteStyles.itemIcon}>
                <Users size={16} />
              </div>
              <div className={quoteStyles.itemDetails}>
                <span className={quoteStyles.itemLabel}>Project Management</span>
                <span className={quoteStyles.itemValue}>{formatHours(quote.aiDriven.projectManagementHours)}</span>
              </div>
              <div className={quoteStyles.itemCost}>
                {formatCurrency(quote.aiDriven.breakdown.projectManagement)}
              </div>
            </div>
            <div className={quoteStyles.breakdownItem}>
              <div className={quoteStyles.itemIcon}>
                <Palette size={16} />
              </div>
              <div className={quoteStyles.itemDetails}>
                <span className={quoteStyles.itemLabel}>Design</span>
                <span className={quoteStyles.itemValue}>{formatHours(quote.aiDriven.designHours)}</span>
              </div>
              <div className={quoteStyles.itemCost}>
                {formatCurrency(quote.aiDriven.breakdown.design)}
              </div>
            </div>
            <div className={quoteStyles.breakdownItem}>
              <div className={quoteStyles.itemIcon}>
                <Bug size={16} />
              </div>
              <div className={quoteStyles.itemDetails}>
                <span className={quoteStyles.itemLabel}>QA Testing</span>
                <span className={quoteStyles.itemValue}>{formatHours(quote.aiDriven.qaHours)}</span>
              </div>
              <div className={quoteStyles.itemCost}>
                {formatCurrency(quote.aiDriven.breakdown.qa)}
              </div>
            </div>
            <div className={quoteStyles.breakdownItem}>
              <div className={quoteStyles.itemIcon}>
                <Server size={16} />
              </div>
              <div className={quoteStyles.itemDetails}>
                <span className={quoteStyles.itemLabel}>Infrastructure</span>
                <span className={quoteStyles.itemValue}>{formatHours(quote.aiDriven.infrastructureHours)}</span>
              </div>
              <div className={quoteStyles.itemCost}>
                {formatCurrency(quote.aiDriven.breakdown.infrastructure)}
              </div>
            </div>
            <div className={quoteStyles.breakdownItem}>
              <div className={quoteStyles.itemIcon}>
                <Wrench size={16} />
              </div>
              <div className={quoteStyles.itemDetails}>
                <span className={quoteStyles.itemLabel}>Maintenance</span>
                <span className={quoteStyles.itemValue}>{formatHours(quote.aiDriven.maintenanceHours)}</span>
              </div>
              <div className={quoteStyles.itemCost}>
                {formatCurrency(quote.aiDriven.breakdown.maintenance)}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className={quoteStyles.summarySection}>
        <div className={quoteStyles.summaryCard}>
          <h4>Why AI-Driven Development Costs Less</h4>
          <ul className={quoteStyles.benefitsList}>
            <li>Automated code generation reduces development time by up to 40%</li>
            <li>AI-powered testing catches bugs earlier, reducing QA overhead</li>
            <li>Intelligent project management tools streamline workflows</li>
            <li>Pre-built AI components accelerate feature development</li>
            <li>Automated documentation and code reviews save time</li>
            <li>Smart infrastructure provisioning reduces setup complexity</li>
            <li>Predictive maintenance reduces long-term costs</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export { QuoteGenerator };
export default QuoteGenerator;
