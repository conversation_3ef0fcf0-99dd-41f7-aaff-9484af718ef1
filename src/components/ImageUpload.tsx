import React, { useRef, useCallback, useState } from 'react';
import { Paperclip, X } from 'lucide-react';
import { ValidationUtils } from '@/utils/validation';
import { LABELS, VALIDATION_MESSAGES } from '@/constants';
import type { ImageUploadProps } from '@/types';
import buttonStyles from '@/styles/buttons.module.scss';
import formStyles from '@/styles/forms.module.scss';

const ImageUpload: React.FC<ImageUploadProps> = ({
  selectedImage,
  onImageChange,
  disabled = false,
  className,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragOver, setDragOver] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFileSelect = useCallback((file: File | null) => {
    if (!file) {
      onImageChange(null);
      setError(null);
      return;
    }

    const validation = ValidationUtils.validateImageFile(file);
    if (!validation.isValid) {
      setError(validation.error || VALIDATION_MESSAGES.INVALID_FILE);
      return;
    }

    setError(null);
    onImageChange(file);
  }, [onImageChange]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    handleFileSelect(file);
  }, [handleFileSelect]);

  const handleAttachClick = useCallback(() => {
    if (disabled) return;
    fileInputRef.current?.click();
  }, [disabled]);

  const handleRemoveImage = useCallback(() => {
    if (disabled) return;
    onImageChange(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [disabled, onImageChange]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    if (disabled) return;

    const files = Array.from(e.dataTransfer.files);
    const imageFile = files.find(file => file.type.startsWith('image/'));
    
    if (imageFile) {
      handleFileSelect(imageFile);
    }
  }, [disabled, handleFileSelect]);

  const getPreviewUrl = useCallback(() => {
    if (!selectedImage) return null;
    return URL.createObjectURL(selectedImage);
  }, [selectedImage]);

  return (
    <div className={className}>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleInputChange}
        className={formStyles.fileInput}
        disabled={disabled}
        aria-label={LABELS.ARIA_UPLOAD_IMAGE_FILE}
      />
      
      <button
        type="button"
        className={buttonStyles.btnOutline}
        onClick={handleAttachClick}
        disabled={disabled}
        aria-label={LABELS.ARIA_ATTACH_IMAGE}
      >
        <Paperclip size={16} style={{ marginRight: '6px' }} />
        {LABELS.ATTACH_IMAGE}
      </button>

      {error && (
        <div className={formStyles.fieldError} role="alert">
          {error}
        </div>
      )}

      {selectedImage && (
        <div 
          className={`${formStyles.imagePreview} ${formStyles.visible}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <img 
            src={getPreviewUrl() || ''} 
            alt={LABELS.IMAGE_PREVIEW_ALT}
            onLoad={() => {
              // Clean up the object URL when image loads
              const url = getPreviewUrl();
              if (url) {
                setTimeout(() => URL.revokeObjectURL(url), 1000);
              }
            }}
          />
          <button
            type="button"
            className={buttonStyles.removeBtn}
            onClick={handleRemoveImage}
            disabled={disabled}
            aria-label={LABELS.ARIA_REMOVE_IMAGE}
          >
            <X size={16} />
          </button>
          <div className={formStyles.imagePreviewOverlay}>
            <span>{LABELS.IMAGE_PREVIEW_REMOVE_TEXT}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export { ImageUpload };
export default ImageUpload;
