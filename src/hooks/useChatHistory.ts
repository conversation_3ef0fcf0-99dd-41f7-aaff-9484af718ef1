import { useState, useCallback, useEffect } from 'react';
import type { ChatHistory, ChatHistoryState, FollowUpMessage } from '@/types';

const STORAGE_KEY = 'ai-chatbot-history';
const MAX_CONVERSATIONS = 50; // Limit stored conversations

const initialState: ChatHistoryState = {
  conversations: [],
  lastConversation: null,
};

export const useChatHistory = () => {
  const [state, setState] = useState<ChatHistoryState>(initialState);

  // Load chat history from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Convert timestamp strings back to Date objects
        const conversations = parsed.conversations.map((conv: any) => ({
          ...conv,
          timestamp: new Date(conv.timestamp),
          followUpHistory: conv.followUpHistory.map((followUp: any) => ({
            ...followUp,
            timestamp: new Date(followUp.timestamp),
          })),
        }));
        
        setState({
          conversations,
          lastConversation: parsed.lastConversation ? {
            ...parsed.lastConversation,
            timestamp: new Date(parsed.lastConversation.timestamp),
            followUpHistory: parsed.lastConversation.followUpHistory.map((followUp: any) => ({
              ...followUp,
              timestamp: new Date(followUp.timestamp),
            })),
          } : null,
        });
      }
    } catch (error) {
      console.warn('Failed to load chat history from localStorage:', error);
    }
  }, []);

  // Save to localStorage whenever state changes
  const saveToStorage = useCallback((newState: ChatHistoryState) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newState));
    } catch (error) {
      console.warn('Failed to save chat history to localStorage:', error);
    }
  }, []);

  // Add a new conversation
  const addConversation = useCallback((
    originalMessage: string,
    originalResponse: string,
    imageAnalyzed: boolean = false,
    conversationId?: string
  ) => {
    const newConversation: ChatHistory = {
      id: `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      originalMessage,
      originalResponse,
      imageAnalyzed,
      followUpHistory: [],
      conversationId,
    };

    setState(prevState => {
      const newConversations = [newConversation, ...prevState.conversations]
        .slice(0, MAX_CONVERSATIONS); // Keep only recent conversations
      
      const newState = {
        conversations: newConversations,
        lastConversation: newConversation,
      };
      
      saveToStorage(newState);
      return newState;
    });

    return newConversation;
  }, [saveToStorage]);

  // Add follow-up to existing conversation
  const addFollowUp = useCallback((
    conversationId: string,
    question: string,
    response: string
  ) => {
    const followUp: FollowUpMessage = {
      id: `followup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      question,
      response,
      timestamp: new Date(),
    };

    setState(prevState => {
      const updatedConversations = prevState.conversations.map(conv => 
        conv.id === conversationId 
          ? { ...conv, followUpHistory: [...conv.followUpHistory, followUp] }
          : conv
      );

      const updatedLastConversation = prevState.lastConversation?.id === conversationId
        ? { ...prevState.lastConversation, followUpHistory: [...prevState.lastConversation.followUpHistory, followUp] }
        : prevState.lastConversation;

      const newState = {
        conversations: updatedConversations,
        lastConversation: updatedLastConversation,
      };

      saveToStorage(newState);
      return newState;
    });

    return followUp;
  }, [saveToStorage]);

  // Get conversation by ID
  const getConversation = useCallback((conversationId: string): ChatHistory | null => {
    return state.conversations.find(conv => conv.id === conversationId) || null;
  }, [state.conversations]);

  // Clear all history
  const clearHistory = useCallback(() => {
    const newState = initialState;
    setState(newState);
    saveToStorage(newState);
  }, [saveToStorage]);

  // Get recent conversations (for potential UI display)
  const getRecentConversations = useCallback((limit: number = 10): ChatHistory[] => {
    return state.conversations.slice(0, limit);
  }, [state.conversations]);

  return {
    conversations: state.conversations,
    lastConversation: state.lastConversation,
    addConversation,
    addFollowUp,
    getConversation,
    clearHistory,
    getRecentConversations,
  };
};

export default useChatHistory;
