# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an AI-powered chatbot application for estimating custom software projects with intelligent conversation management. The application uses React TypeScript frontend with a Node.js Express backend that integrates with OpenAI's API.

**Key Innovation**: The AI is designed to detect when users provide comprehensive responses (like "all of those things") and switch from questioning mode to solution mode, preventing endless question loops.

## Architecture

### Monorepo Structure
- **Frontend**: React 18 + TypeScript 5 + Vite (root directory)
- **Backend**: Node.js + Express + TypeScript (backend/ directory)
- **Shared**: Both use ES modules (`"type": "module"`)

### Frontend Architecture
```
src/
├── components/          # React components with conversation intelligence
├── hooks/              # Custom hooks for state and conversation management
├── services/           # API communication with conversation context
├── constants/          # Centralized messages, labels, and validation
├── styles/            # SCSS modules with CSS variables
├── types/             # TypeScript definitions including conversation types
└── utils/             # Validation and DOM utilities
```

### Backend Architecture
```
backend/
├── prompts/           # Intelligent prompt system with conversation analysis
├── services/          # Core business logic including conversation management
├── routes/            # Express routes with middleware
├── middleware/        # CORS, error handling, file uploads
└── chats/             # Persistent conversation storage (JSON files)
```

## Development Commands

### Root Level Commands
- `npm run dev` - Start both frontend (port 5173) and backend (port 3001) concurrently
- `npm run dev:frontend` - Start only Vite dev server
- `npm run dev:backend` - Start only backend with tsx watch
- `npm run build` - Build frontend for production
- `npm run type-check` - TypeScript type checking without emit
- `npm install:backend` - Install backend dependencies

### Backend Commands (from backend/ directory)
- `npm run dev` - Development server with tsx watch
- `npm run build` - Compile TypeScript to JavaScript
- `npm start` - Production server (build + run)

## Key Technologies & Patterns

### Frontend Stack
- **React 18** with functional components and hooks
- **TypeScript** with strict mode and path aliases (`@/` prefix)
- **Vite** for build tooling with React plugin
- **SCSS Modules** with CSS variables for theming
- **Lucide React** for icons

### Backend Stack  
- **Express** server with TypeScript compilation
- **OpenAI API** (GPT-4) with conversation context
- **Multer** for file upload handling
- **CORS** middleware for cross-origin requests

### State Management Pattern
The application uses a custom `useStateManager` hook with conversation tracking via `useChatHistory`. Conversations are persisted as JSON files in `backend/chats/`.

### Conversation Intelligence System
Located in `backend/prompts/`, this system:
- Analyzes user responses for comprehensive indicators
- Dynamically generates system prompts based on conversation state
- Switches between questioning mode and solution mode

## Environment Setup

### Frontend (.env in root)
```
VITE_UNSPLASH_ACCESS_KEY=your_key_here  # Optional for backgrounds
```

### Backend (.env in backend/)
```
OPENAI_API_KEY=your_key_here  # Required
PORT=3001                     # Optional, defaults to 3001
```

## Code Conventions

### TypeScript Configuration
- Uses strict mode with `noUnusedLocals` and `noUnusedParameters`
- Path aliases configured for cleaner imports
- React JSX transform enabled

### Component Patterns
- Functional components with TypeScript interfaces
- Props destructuring with explicit typing
- Custom hooks for reusable logic
- CSS Modules for scoped styling

### API Communication
- All API calls go through `src/services/api.ts`
- Conversation context is maintained across requests
- Error handling with user-friendly notifications

## Testing & Quality

There are no configured testing frameworks. When adding tests:
- Check for existing test scripts in package.json
- Look for test configuration files
- Ask user for preferred testing approach

## Important Notes

- The application expects OpenAI API key for core functionality
- Conversation history is stored locally in JSON files
- Image uploads are handled via multer with temporary storage
- The AI system has specific response length limits (500 characters, 3 sentences)
- All file paths use absolute imports with `@/` prefix on frontend