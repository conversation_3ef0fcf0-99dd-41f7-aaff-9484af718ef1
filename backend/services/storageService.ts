import fs from 'fs';
import fsPromises from 'fs/promises';
import path from 'path';

export class StorageService {
  static async saveChatSession(messages: any[], format: string | undefined): Promise<void> {
    const chatsDir = path.join(process.cwd(), 'chats');
    if (!fs.existsSync(chatsDir)) {
      fs.mkdirSync(chatsDir, { recursive: true });
    }
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const random = Math.floor(Math.random() * 1e6);
    const filename = `chat_${timestamp}_${random}.json`;
    const filePath = path.join(chatsDir, filename);
    const data = {
      createdAt: new Date().toISOString(),
      format,
      messages
    };
    await fsPromises.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8');
  }
}
