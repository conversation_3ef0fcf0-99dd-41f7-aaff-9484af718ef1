import express from 'express';
import dotenv from 'dotenv';
import { corsMiddleware } from './middleware/corsMiddleware.js';
import { errorMiddleware } from './middleware/errorMiddleware.js';
import routes from './routes/index.js';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(corsMiddleware);
app.use(express.json());

// Routes
app.use(routes);

// Error handling middleware
app.use(errorMiddleware);

app.listen(PORT, () => {
  console.log(`🚀 AI Chatbot API server running on http://localhost:${PORT}`);
  console.log(`📝 Health check: http://localhost:${PORT}/api/health`);
});
